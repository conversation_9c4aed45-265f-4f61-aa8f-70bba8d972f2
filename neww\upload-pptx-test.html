<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎥 Test PPTX Upload</title>
    <style>
        * { box-sizing: border-box; }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .upload-zone {
            border: 3px dashed #4672c4;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f8f9ff;
            margin-bottom: 20px;
            cursor: pointer;
        }
        
        .upload-button {
            background: #4672c4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
        }
        
        .demo-button {
            background: #28a745;
        }
        
        .file-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            display: none;
        }
        
        .error-message {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
        }

        .success-message {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
        }

        .processing {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            display: none;
            text-align: center;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4672c4;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .demo-slides {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            display: none;
        }

        .slide-preview {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            margin: 8px 0;
            background: white;
        }

        .slide-number {
            background: #4672c4;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Test PPTX Upload</h1>
        
        <div class="upload-zone" id="uploadZone">
            <div style="font-size: 48px; margin-bottom: 15px;">📁</div>
            <div style="font-size: 18px; margin-bottom: 15px;">
                Arrastra tu archivo PPTX aquí o haz click para seleccionar
            </div>
            <button class="upload-button" onclick="selectFile()">
                Seleccionar Archivo
            </button>
            <button class="upload-button demo-button" onclick="loadDemoFile()">
                📁 Cargar Demo PPTX
            </button>
            <input type="file" id="fileInput" accept=".pptx,.ppt" style="display: none;">
        </div>
        
        <div class="file-info" id="fileInfo">
            <h3>📄 Archivo Seleccionado:</h3>
            <p id="fileName"></p>
            <p id="fileSize"></p>
            <button class="upload-button" onclick="processFile()">
                🚀 Procesar Presentación
            </button>
        </div>

        <div class="processing" id="processing">
            <div class="spinner"></div>
            <h3>🔄 Procesando PPTX...</h3>
            <p>Extrayendo slides, videos y contenido multimedia...</p>
        </div>

        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>

        <div class="demo-slides" id="demoSlides">
            <h3>📊 Contenido Extraído:</h3>
            <div id="slidesList"></div>
            <button class="upload-button" onclick="launchSlideshow()" style="margin-top: 15px;">
                🎬 Iniciar Slideshow
            </button>
        </div>
    </div>

    <script>
        console.log('🚀 Script iniciado - versión limpia');

        let selectedFile = null;
        let processedSlides = [];
        let currentSlideIndex = 0;
        
        // Función demo
        async function loadDemoFile() {
            console.log('🎯 loadDemoFile ejecutada!');

            try {
                // Intentar cargar el archivo real desde public
                const response = await fetch('./public/Power of You Video Version 07.08.25.pptx');
                if (response.ok) {
                    const blob = await response.blob();
                    const file = new File([blob], 'Power of You Video Version 07.08.25.pptx', {
                        type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
                    });
                    console.log('✅ Archivo demo cargado:', file.name, file.size, 'bytes');
                    handleFileSelect(file);
                } else {
                    throw new Error('No se pudo cargar el archivo demo');
                }
            } catch (error) {
                console.log('⚠️ No se pudo cargar archivo real, usando simulado');
                // Fallback: crear archivo simulado
                const demoFile = new File(['contenido demo'], 'demo.pptx', {
                    type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
                });
                handleFileSelect(demoFile);
            }
        }
        
        // Función para seleccionar archivo
        function selectFile() {
            console.log('📁 Abriendo selector de archivos');
            document.getElementById('fileInput').click();
        }
        
        // Manejar selección de archivo
        function handleFileSelect(file) {
            console.log('📁 Archivo seleccionado:', file.name);
            
            selectedFile = file;
            document.getElementById('fileName').textContent = 'Nombre: ' + file.name;
            document.getElementById('fileSize').textContent = 'Tamaño: ' + (file.size / 1024 / 1024).toFixed(2) + ' MB';
            document.getElementById('fileInfo').style.display = 'block';
            hideError();
        }
        
        // Procesar archivo
        function processFile() {
            if (!selectedFile) {
                showError('No hay archivo seleccionado.');
                return;
            }

            console.log('🚀 Procesando archivo:', selectedFile.name);

            // Mostrar indicador de procesamiento
            document.getElementById('processing').style.display = 'block';
            document.getElementById('fileInfo').style.display = 'none';
            hideError();
            hideSuccess();

            // Simular procesamiento después de 2 segundos
            setTimeout(() => {
                simulateProcessing();
            }, 2000);
        }

        function simulateProcessing() {
            const slideCount = Math.floor(Math.random() * 8) + 3; // 3-10 slides
            processedSlides = [];

            for (let i = 0; i < slideCount; i++) {
                const slideTypes = ['image', 'video', 'mixed'];
                const randomType = slideTypes[Math.floor(Math.random() * slideTypes.length)];

                let slide = {
                    type: randomType,
                    title: 'Slide ' + (i + 1) + ': ' + getSlideTitle(randomType),
                    hasVideo: randomType !== 'image'
                };

                if (randomType === 'image') {
                    slide.imgUrl = 'https://via.placeholder.com/800x600/4672c4/ffffff?text=Slide+' + (i + 1);
                } else if (randomType === 'video') {
                    slide.videoUrl = getRandomVideoUrl();
                    slide.thumbnailUrl = 'https://via.placeholder.com/800x600/f57c00/ffffff?text=Video+' + (i + 1);
                } else if (randomType === 'mixed') {
                    slide.imgUrl = 'https://via.placeholder.com/800x600/7b1fa2/ffffff?text=Mixed+' + (i + 1);
                    slide.videoUrl = getRandomVideoUrl();
                }

                processedSlides.push(slide);
            }

            document.getElementById('processing').style.display = 'none';
            showProcessedSlides();
            showSuccess('¡Procesamiento completado! Se extrajeron ' + slideCount + ' slides con ' + processedSlides.filter(s => s.hasVideo).length + ' videos.');
        }

        function getSlideTitle(type) {
            const titles = {
                image: 'Contenido Visual',
                video: 'Video Embebido',
                mixed: 'Contenido Mixto'
            };
            return titles[type] || 'Slide';
        }

        function getRandomVideoUrl() {
            const videos = [
                'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
                'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4'
            ];
            return videos[Math.floor(Math.random() * videos.length)];
        }

        function showProcessedSlides() {
            const slidesList = document.getElementById('slidesList');
            slidesList.innerHTML = '';

            processedSlides.forEach((slide, index) => {
                const slideDiv = document.createElement('div');
                slideDiv.className = 'slide-preview';
                slideDiv.innerHTML =
                    '<div class="slide-number">' + (index + 1) + '</div>' +
                    '<div style="flex: 1;">' +
                        '<strong>' + slide.title + '</strong><br>' +
                        '<small>' + (slide.hasVideo ? '🎥 Contiene video' : '🖼️ Solo imagen') + '</small>' +
                    '</div>';
                slidesList.appendChild(slideDiv);
            });

            document.getElementById('demoSlides').style.display = 'block';
        }

        function launchSlideshow() {
            alert('🎬 ¡Slideshow iniciado con ' + processedSlides.length + ' slides!');
            console.log('Slides procesados:', processedSlides);
        }
        
        // Mostrar error
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        
        // Ocultar error
        function hideError() {
            document.getElementById('errorMessage').style.display = 'none';
        }
        
        // Event listeners
        document.getElementById('fileInput').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });
        
        console.log('✅ Script cargado completamente');
    </script>
</body>
</html>
