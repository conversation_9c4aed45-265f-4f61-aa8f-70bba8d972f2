<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎥 Test PPTX Upload</title>
    <style>
        * { box-sizing: border-box; }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .upload-zone {
            border: 3px dashed #4672c4;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f8f9ff;
            margin-bottom: 20px;
            cursor: pointer;
        }
        
        .upload-button {
            background: #4672c4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
        }
        
        .demo-button {
            background: #28a745;
        }
        
        .file-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            display: none;
        }
        
        .error-message {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Test PPTX Upload</h1>
        
        <div class="upload-zone" id="uploadZone">
            <div style="font-size: 48px; margin-bottom: 15px;">📁</div>
            <div style="font-size: 18px; margin-bottom: 15px;">
                Arrastra tu archivo PPTX aquí o haz click para seleccionar
            </div>
            <button class="upload-button" onclick="selectFile()">
                Seleccionar Archivo
            </button>
            <button class="upload-button demo-button" onclick="loadDemoFile()">
                📁 Cargar Demo PPTX
            </button>
            <input type="file" id="fileInput" accept=".pptx,.ppt" style="display: none;">
        </div>
        
        <div class="file-info" id="fileInfo">
            <h3>📄 Archivo Seleccionado:</h3>
            <p id="fileName"></p>
            <p id="fileSize"></p>
            <button class="upload-button" onclick="processFile()">
                🚀 Procesar Presentación
            </button>
        </div>
        
        <div class="error-message" id="errorMessage"></div>
    </div>

    <script>
        console.log('🚀 Script iniciado - versión limpia');
        
        let selectedFile = null;
        
        // Función demo
        function loadDemoFile() {
            console.log('🎯 loadDemoFile ejecutada!');
            alert('¡La función demo funciona!');
            
            // Crear archivo simulado
            const demoFile = new File(['contenido demo'], 'demo.pptx', {
                type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
            });
            
            handleFileSelect(demoFile);
        }
        
        // Función para seleccionar archivo
        function selectFile() {
            console.log('📁 Abriendo selector de archivos');
            document.getElementById('fileInput').click();
        }
        
        // Manejar selección de archivo
        function handleFileSelect(file) {
            console.log('📁 Archivo seleccionado:', file.name);
            
            selectedFile = file;
            document.getElementById('fileName').textContent = 'Nombre: ' + file.name;
            document.getElementById('fileSize').textContent = 'Tamaño: ' + (file.size / 1024 / 1024).toFixed(2) + ' MB';
            document.getElementById('fileInfo').style.display = 'block';
            hideError();
        }
        
        // Procesar archivo
        function processFile() {
            if (!selectedFile) {
                showError('No hay archivo seleccionado.');
                return;
            }
            
            console.log('🚀 Procesando archivo:', selectedFile.name);
            alert('¡Procesando archivo: ' + selectedFile.name + '!');
        }
        
        // Mostrar error
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        
        // Ocultar error
        function hideError() {
            document.getElementById('errorMessage').style.display = 'none';
        }
        
        // Event listeners
        document.getElementById('fileInput').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });
        
        console.log('✅ Script cargado completamente');
    </script>
</body>
</html>
