<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎥 Subir PPTX - Demo Completo</title>
    <style>
        * { box-sizing: border-box; }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .upload-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .upload-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .upload-zone {
            border: 3px dashed #4672c4;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f8f9ff;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upload-zone:hover { border-color: #3f51b5; background: #f0f2ff; }
        .upload-zone.dragover { border-color: #28a745; background: #f0fff4; }
        
        .upload-button {
            background: #4672c4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upload-button:hover {
            background: #3f51b5;
            transform: translateY(-2px);
        }
        
        .file-info, .processing, .demo-slides {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            display: none;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4672c4;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .slide-preview {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            margin: 8px 0;
            background: white;
        }
        
        .slide-number {
            background: #4672c4;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
        
        .slide-type {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .type-image { background: #e3f2fd; color: #1976d2; }
        .type-video { background: #fff3e0; color: #f57c00; }
        .type-mixed { background: #f3e5f5; color: #7b1fa2; }
        
        /* Slideshow Styles */
        .slideshow-container {
            margin-top: 30px;
            background: #000;
            border-radius: 10px;
            padding: 20px;
            display: none;
            min-height: 600px;
        }
        
        .slideshow {
            display: flex;
            height: 600px;
            justify-content: center;
            align-items: center;
            position: relative;
        }
        
        .slideshow__container {
            display: flex;
            align-items: center;
            width: 100%;
            max-width: 1200px;
            height: 100%;
        }
        
        .slideshow__content {
            flex: 1;
            height: 100%;
            display: flex;
            flex-direction: column;
            margin: 0 20px;
        }
        
        .slideshow__main-content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #222;
            border-radius: 8px;
            position: relative;
        }
        
        .slideshow__slide {
            display: none;
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .slideshow__slide.active {
            display: block;
        }
        
        .slideshow__slide video {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .slideshow__mixed-content {
            position: relative;
            width: 100%;
            height: 100%;
        }
        
        .slideshow__mixed-content img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .slideshow__mixed-content video {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 30%;
            height: 30%;
            border: 2px solid white;
            border-radius: 8px;
        }
        
        .slideshow__controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            margin-top: 10px;
        }
        
        .slideshow__nav-btn {
            background: #4672c4;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
        }
        
        .slideshow__nav-btn:hover {
            background: #3f51b5;
        }
        
        .slideshow__nav-btn:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .slideshow__info {
            color: white;
            text-align: center;
        }
        
        .slideshow__thumbnails {
            width: 200px;
            height: 100%;
            overflow-y: auto;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
        
        .slideshow__thumbnail {
            width: 100%;
            height: 80px;
            margin-bottom: 10px;
            border: 2px solid transparent;
            border-radius: 6px;
            cursor: pointer;
            object-fit: cover;
            transition: all 0.3s ease;
        }
        
        .slideshow__thumbnail.active {
            border-color: #4672c4;
        }
        
        .slideshow__thumbnail:hover {
            border-color: #fff;
        }
        
        .slideshow__notes {
            width: 250px;
            height: 100%;
            overflow-y: auto;
            padding: 15px;
            background: rgba(255,255,255,0.95);
            border-radius: 8px;
            color: #333;
        }
        
        .video-indicator {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .error-message, .success-message {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
        }
        
        .error-message {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
        
        .success-message {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        
        @media (max-width: 768px) {
            .slideshow__container {
                flex-direction: column;
            }
            
            .slideshow__thumbnails,
            .slideshow__notes {
                width: 100%;
                height: 150px;
                margin-top: 10px;
            }
            
            .slideshow__thumbnails {
                display: flex;
                overflow-x: auto;
                overflow-y: hidden;
            }
            
            .slideshow__thumbnail {
                min-width: 100px;
                margin-right: 10px;
                margin-bottom: 0;
            }
        }
    </style>
</head>
<body>
    <div class="upload-container">
        <div class="upload-header">
            <h1>🎥 Subir Presentación PPTX</h1>
            <p>Sube tu archivo PPTX para convertirlo en slideshow interactivo con videos</p>
        </div>
        
        <div class="upload-zone" id="uploadZone">
            <div style="font-size: 48px; margin-bottom: 15px;">📁</div>
            <div style="font-size: 18px; margin-bottom: 15px;">
                Arrastra tu archivo PPTX aquí o haz click para seleccionar
            </div>
            <button class="upload-button" onclick="document.getElementById('fileInput').click()">
                Seleccionar Archivo
            </button>
            <input type="file" id="fileInput" accept=".pptx,.ppt" style="display: none;">
        </div>
        
        <div class="file-info" id="fileInfo">
            <h3>📄 Archivo Seleccionado:</h3>
            <p id="fileName"></p>
            <p id="fileSize"></p>
            <button class="upload-button" onclick="processFile()">
                🚀 Procesar Presentación
            </button>
        </div>
        
        <div class="processing" id="processing">
            <div style="text-align: center;">
                <div class="spinner"></div>
                <h3>🔄 Procesando PPTX...</h3>
                <p>Extrayendo slides, videos y contenido multimedia...</p>
            </div>
        </div>
        
        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>
        
        <div class="demo-slides" id="demoSlides">
            <h3>📊 Contenido Extraído (Simulado):</h3>
            <div id="slidesList"></div>
            <button class="upload-button" onclick="launchSlideshow()" style="margin-top: 15px;">
                🎬 Iniciar Slideshow
            </button>
        </div>
        
        <div class="slideshow-container" id="slideshowContainer">
            <div class="slideshow">
                <div class="slideshow__container">
                    <div class="slideshow__thumbnails" id="thumbnails"></div>
                    
                    <div class="slideshow__content">
                        <div class="slideshow__main-content" id="mainContent">
                            <!-- Los slides se cargarán aquí -->
                        </div>
                        
                        <div class="slideshow__controls">
                            <button class="slideshow__nav-btn" id="prevBtn" onclick="previousSlide()">◀ Anterior</button>
                            <div class="slideshow__info">
                                <span id="slideInfo">Slide 1 / 1</span>
                                <br>
                                <button class="upload-button" onclick="openStudentWindow()" style="margin-top: 5px; padding: 5px 10px; font-size: 14px;">
                                    👥 Ventana Estudiante
                                </button>
                            </div>
                            <button class="slideshow__nav-btn" id="nextBtn" onclick="nextSlide()">Siguiente ▶</button>
                        </div>
                    </div>
                    
                    <div class="slideshow__notes" id="notesPanel">
                        <h4>📝 Notas del Slide</h4>
                        <div id="slideNotes">Selecciona un slide para ver las notas.</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('🚀 Script iniciado');
        let selectedFile = null;
        let processedSlides = [];
        let currentSlideIndex = 0;
        let studentWindow = null;

        // Configurar drag & drop
        const uploadZone = document.getElementById('uploadZone');
        const fileInput = document.getElementById('fileInput');
        console.log('Elements found:', uploadZone, fileInput);

        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        });

        uploadZone.addEventListener('dragleave', () => {
            uploadZone.classList.remove('dragover');
        });

        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        uploadZone.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            console.log('File input changed:', e.target.files);
            if (e.target.files.length > 0) {
                console.log('File selected:', e.target.files[0]);
                handleFileSelect(e.target.files[0]);
            }
        });

        function handleFileSelect(file) {
            console.log('handleFileSelect called with:', file);
            // Validar tipo de archivo
            const validExtensions = ['.pptx', '.ppt'];
            const fileName = file.name.toLowerCase();
            const isValidType = validExtensions.some(ext => fileName.endsWith(ext));
            console.log('File validation:', fileName, isValidType);

            if (!isValidType) {
                showError('Por favor selecciona un archivo PPTX o PPT válido.');
                return;
            }

            // Validar tamaño de archivo (máximo 50MB)
            const maxSize = 50 * 1024 * 1024; // 50MB en bytes
            if (file.size > maxSize) {
                showError('El archivo es demasiado grande. El tamaño máximo permitido es 50MB.');
                return;
            }

            selectedFile = file;
            document.getElementById('fileName').textContent = `Nombre: ${file.name}`;
            document.getElementById('fileSize').textContent = `Tamaño: ${(file.size / 1024 / 1024).toFixed(2)} MB`;
            document.getElementById('fileInfo').style.display = 'block';
            hideMessages();
        }

        function processFile() {
            if (!selectedFile) {
                showError('No hay archivo seleccionado.');
                return;
            }

            document.getElementById('processing').style.display = 'block';
            document.getElementById('fileInfo').style.display = 'none';
            hideMessages();

            setTimeout(() => {
                simulateProcessing();
            }, 2000);
        }

        function simulateProcessing() {
            const slideCount = Math.floor(Math.random() * 8) + 3;
            processedSlides = [];

            for (let i = 0; i < slideCount; i++) {
                const slideTypes = ['image', 'video', 'mixed'];
                const randomType = slideTypes[Math.floor(Math.random() * slideTypes.length)];
                
                let slide = {
                    type: randomType,
                    title: `Slide ${i + 1}: ${getSlideTitle(randomType)}`,
                    note: `<h3>Slide ${i + 1}: ${getSlideTitle(randomType)}</h3><p>Contenido extraído del archivo PPTX: <strong>${selectedFile.name}</strong></p><ul><li>Punto importante 1</li><li>Punto importante 2</li><li>Información relevante del slide</li></ul>`,
                    hasVideo: randomType !== 'image'
                };

                if (randomType === 'image') {
                    slide.imgUrl = 'https://via.placeholder.com/800x600/4672c4/ffffff?text=Slide+' + (i + 1);
                } else if (randomType === 'video') {
                    slide.videoUrl = getRandomVideoUrl();
                    slide.thumbnailUrl = 'https://via.placeholder.com/800x600/f57c00/ffffff?text=Video+' + (i + 1);
                } else if (randomType === 'mixed') {
                    slide.imgUrl = 'https://via.placeholder.com/800x600/7b1fa2/ffffff?text=Mixed+' + (i + 1);
                    slide.videoUrl = getRandomVideoUrl();
                    slide.isMixed = true;
                }

                processedSlides.push(slide);
            }

            document.getElementById('processing').style.display = 'none';
            showProcessedSlides();
            showSuccess(`¡Procesamiento completado! Se extrajeron ${slideCount} slides con ${processedSlides.filter(s => s.hasVideo).length} videos.`);
        }

        function getSlideTitle(type) {
            const titles = {
                image: 'Contenido Visual',
                video: 'Video Embebido',
                mixed: 'Contenido Mixto'
            };
            return titles[type] || 'Slide';
        }

        function getRandomVideoUrl() {
            const videos = [
                'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
                'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4'
            ];
            return videos[Math.floor(Math.random() * videos.length)];
        }

        function showProcessedSlides() {
            const slidesList = document.getElementById('slidesList');
            slidesList.innerHTML = '';

            processedSlides.forEach((slide, index) => {
                const slideDiv = document.createElement('div');
                slideDiv.className = 'slide-preview';
                slideDiv.innerHTML = `
                    <div class="slide-number">${index + 1}</div>
                    <div style="flex: 1;">
                        <strong>${slide.title}</strong>
                        <span class="slide-type type-${slide.type}">${slide.type.toUpperCase()}</span>
                        <br>
                        <small>${slide.hasVideo ? '🎥 Contiene video' : '🖼️ Solo imagen'}</small>
                    </div>
                `;
                slidesList.appendChild(slideDiv);
            });

            document.getElementById('demoSlides').style.display = 'block';
        }

        function launchSlideshow() {
            document.getElementById('slideshowContainer').style.display = 'block';
            document.getElementById('slideshowContainer').scrollIntoView({ behavior: 'smooth' });
            
            initializeSlideshow();
        }

        function initializeSlideshow() {
            const mainContent = document.getElementById('mainContent');
            const thumbnails = document.getElementById('thumbnails');
            
            // Limpiar contenido anterior
            mainContent.innerHTML = '';
            thumbnails.innerHTML = '';
            
            // Crear slides
            processedSlides.forEach((slide, index) => {
                // Crear slide principal
                const slideElement = document.createElement('div');
                slideElement.className = 'slideshow__slide';
                slideElement.id = `slide-${index}`;
                
                if (slide.type === 'video') {
                    slideElement.innerHTML = `
                        <video controls muted style="width: 100%; height: 100%; object-fit: contain;" preload="metadata">
                            <source src="${slide.videoUrl}" type="video/mp4">
                            Tu navegador no soporta video HTML5.
                        </video>
                    `;
                } else if (slide.type === 'mixed') {
                    slideElement.innerHTML = `
                        <div class="slideshow__mixed-content">
                            <img src="${slide.imgUrl}" alt="Slide ${index + 1}" loading="lazy">
                            <video controls muted preload="metadata">
                                <source src="${slide.videoUrl}" type="video/mp4">
                                Tu navegador no soporta video HTML5.
                            </video>
                        </div>
                    `;
                } else {
                    slideElement.innerHTML = `
                        <img src="${slide.imgUrl}" alt="Slide ${index + 1}" style="width: 100%; height: 100%; object-fit: contain;" loading="lazy">
                    `;
                }
                
                mainContent.appendChild(slideElement);
                
                // Crear thumbnail
                const thumbnail = document.createElement('div');
                thumbnail.style.position = 'relative';
                thumbnail.innerHTML = `
                    <img src="${slide.thumbnailUrl || slide.imgUrl}" 
                         alt="Slide ${index + 1}" 
                         class="slideshow__thumbnail" 
                         onclick="goToSlide(${index})">
                    ${slide.hasVideo ? '<div class="video-indicator">▶</div>' : ''}
                `;
                thumbnails.appendChild(thumbnail);
            });
            
            // Mostrar primer slide
            goToSlide(0);
            
            // Configurar navegación con teclado
            document.addEventListener('keydown', handleKeyNavigation);
            
            console.log('🎬 Slideshow inicializado con', processedSlides.length, 'slides');
        }

        function goToSlide(index) {
            if (index < 0 || index >= processedSlides.length) return;
            
            // Ocultar slide actual
            const currentSlide = document.querySelector('.slideshow__slide.active');
            if (currentSlide) {
                currentSlide.classList.remove('active');
                // Pausar videos del slide anterior
                const videos = currentSlide.querySelectorAll('video');
                videos.forEach(video => {
                    video.pause();
                    video.currentTime = 0;
                });
            }
            
            // Mostrar nuevo slide
            const newSlide = document.getElementById(`slide-${index}`);
            if (newSlide) {
                newSlide.classList.add('active');
                // Reproducir videos del nuevo slide
                const videos = newSlide.querySelectorAll('video');
                videos.forEach(video => {
                    video.muted = true;
                    video.addEventListener('loadeddata', () => {
                        video.play().catch(e => console.log('Autoplay prevented:', e));
                    });
                    video.addEventListener('error', (e) => {
                        console.error('Error loading video:', e);
                        video.style.display = 'none';
                        const errorMsg = document.createElement('div');
                        errorMsg.style.cssText = 'color: white; text-align: center; padding: 20px;';
                        errorMsg.textContent = 'Error al cargar el video';
                        video.parentNode.appendChild(errorMsg);
                    });
                    // Try to play immediately if already loaded
                    if (video.readyState >= 3) {
                        video.play().catch(e => console.log('Autoplay prevented:', e));
                    }
                });
            }
            
            // Actualizar thumbnails
            document.querySelectorAll('.slideshow__thumbnail').forEach((thumb, i) => {
                thumb.classList.toggle('active', i === index);
            });
            
            // Actualizar información
            currentSlideIndex = index;
            document.getElementById('slideInfo').textContent = `Slide ${index + 1} / ${processedSlides.length}`;
            document.getElementById('slideNotes').innerHTML = processedSlides[index].note;
            
            // Actualizar botones
            document.getElementById('prevBtn').disabled = index === 0;
            document.getElementById('nextBtn').disabled = index === processedSlides.length - 1;
            
            // Sincronizar con ventana de estudiante
            syncStudentWindow();
        }

        function previousSlide() {
            if (currentSlideIndex > 0) {
                goToSlide(currentSlideIndex - 1);
            }
        }

        function nextSlide() {
            if (currentSlideIndex < processedSlides.length - 1) {
                goToSlide(currentSlideIndex + 1);
            }
        }

        function handleKeyNavigation(e) {
            if (e.key === 'ArrowLeft') {
                previousSlide();
            } else if (e.key === 'ArrowRight') {
                nextSlide();
            }
        }

        function openStudentWindow() {
            if (studentWindow && !studentWindow.closed) {
                studentWindow.focus();
                return;
            }
            
            const width = window.screen.width * 0.6;
            const height = window.screen.height * 0.6;
            
            studentWindow = window.open(
                'about:blank',
                'StudentWindow',
                `width=${width},height=${height},scrollbars=yes,resizable=yes`
            );
            
            const studentHTML = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Ventana de Estudiante</title>
                    <style>
                        body { margin: 0; padding: 0; background: #000; display: flex; align-items: center; justify-content: center; height: 100vh; }
                        .content { width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; }
                        img, video { max-width: 100%; max-height: 100%; object-fit: contain; }
                        .mixed-content { position: relative; width: 100%; height: 100%; }
                        .mixed-content img { width: 100%; height: 100%; object-fit: contain; }
                        .mixed-content video { position: absolute; bottom: 20px; right: 20px; width: 30%; height: 30%; border: 2px solid white; }
                    </style>
                </head>
                <body>
                    <div class="content" id="studentContent">
                        <div style="color: white; text-align: center;">
                            <h2>Ventana de Estudiante</h2>
                            <p>El contenido se sincronizará automáticamente</p>
                        </div>
                    </div>
                    <script>
                        window.addEventListener('message', function(event) {
                            if (event.data.type === 'slideUpdate') {
                                updateStudentContent(event.data.slide);
                            }
                        });

                        function updateStudentContent(slide) {
                            const content = document.getElementById('studentContent');

                            if (slide.type === 'video') {
                                content.innerHTML = '<video controls muted style="width: 100%; height: 100%; object-fit: contain;" preload="metadata"><source src="' + slide.videoUrl + '" type="video/mp4">Tu navegador no soporta video HTML5.</source></video>';
                            } else if (slide.type === 'mixed') {
                                content.innerHTML = '<div class="mixed-content"><img src="' + slide.imgUrl + '" loading="lazy"><video controls muted preload="metadata"><source src="' + slide.videoUrl + '" type="video/mp4">Tu navegador no soporta video HTML5.</source></video></div>';
                            } else {
                                content.innerHTML = '<img src="' + slide.imgUrl + '" alt="Slide" loading="lazy">';
                            }
                        }
                    <\/script>
                </body>
                </html>
            `;
            
            studentWindow.document.write(studentHTML);
            studentWindow.document.close();
            
            // Sincronizar slide actual
            syncStudentWindow();
        }

        function syncStudentWindow() {
            if (studentWindow && !studentWindow.closed && processedSlides[currentSlideIndex]) {
                studentWindow.postMessage({
                    type: 'slideUpdate',
                    slide: processedSlides[currentSlideIndex]
                }, '*');
            }
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }

        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }

        // Inicialización
        console.log('🚀 Sistema de slideshow con videos cargado correctamente');
    </script>
</body>
</html>
