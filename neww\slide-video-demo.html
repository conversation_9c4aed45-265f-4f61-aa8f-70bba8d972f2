<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Slideshow con Videos</title>
    <link rel="stylesheet" href="slideshow-video.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .demo-info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-info h3 {
            color: #333;
            margin-top: 0;
        }
        .demo-info ul {
            color: #666;
        }
        .demo-info li {
            margin-bottom: 8px;
        }
        .feature-badge {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 10px;
        }
        .slideshow-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
    </style>
    <!-- Incluir archivos JavaScript -->
    <script src="slideshow-video.js"></script>
    <script>
        // Simulación de window.tool para el demo
        window.tool = {
            hasClassName: (event, className) => {
                return event.target.classList.contains(className);
            },
            template: (array, callback) => {
                return array.map(callback).join('');
            }
        };
    </script>
</head>
<body>
    <div class="demo-header">
        <h1>🎥 Demo: Slideshow con Videos</h1>
        <p>Extensión de tu sistema actual para soportar contenido multimedia de PPTX</p>
    </div>

    <div class="demo-info">
        <h3>🚀 Nuevas Características Implementadas:</h3>
        <ul>
            <li><strong>Videos embebidos</strong> - Reproducción directa de videos extraídos de PPTX <span class="feature-badge">NUEVO</span></li>
            <li><strong>Contenido mixto</strong> - Slides con imagen + video superpuesto <span class="feature-badge">NUEVO</span></li>
            <li><strong>Indicadores visuales</strong> - Iconos ▶ en miniaturas con video <span class="feature-badge">NUEVO</span></li>
            <li><strong>Sincronización</strong> - Videos sincronizados entre ventana principal y estudiante <span class="feature-badge">NUEVO</span></li>
            <li><strong>Controles inteligentes</strong> - Pausa automática al cambiar slides <span class="feature-badge">NUEVO</span></li>
            <li><strong>Responsive</strong> - Videos adaptativos en móviles y tablets <span class="feature-badge">NUEVO</span></li>
        </ul>
    </div>

    <div class="demo-info">
        <h3>📋 Datos de Prueba Simulados:</h3>
        <ul>
            <li><strong>Slide 1:</strong> Imagen tradicional (Google Drive)</li>
            <li><strong>Slide 2:</strong> Video completo con controles</li>
            <li><strong>Slide 3:</strong> Imagen con gráficos</li>
            <li><strong>Slide 4:</strong> Segundo video (Big Buck Bunny)</li>
            <li><strong>Slide 5:</strong> Contenido mixto (imagen + video superpuesto)</li>
        </ul>
    </div>

    <div class="slideshow-container">
        <!-- Aquí se cargará el slideshow -->
        <div class="demo-slideshow" style="height: 725px; display: flex; justify-content: center; align-items: center; margin: 50px 0;"></div>
    </div>

    <!-- Botón para copiar enlace -->
    <div style="text-align: center; margin-top: 20px;">
        <button id="copyLinkBtn" style="padding: 12px 24px; background-color: #28a745; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px;">
            📋 Copiar Enlace de Encuesta
        </button>
        <span id="copySuccess" style="color: green; margin-left: 10px; display: none; font-weight: bold;">¡Link copiado!</span>
    </div>

    <script>
        // Script para copiar enlace
        document.getElementById('copyLinkBtn').addEventListener('click', function() {
            const surveyLink = "https://culturaviva.org/student-survey/";
            const tempInput = document.createElement("input");
            tempInput.value = surveyLink;
            document.body.appendChild(tempInput);
            tempInput.select();
            document.execCommand("copy");
            document.body.removeChild(tempInput);
            
            const successMessage = document.getElementById("copySuccess");
            successMessage.style.display = "inline";
            setTimeout(() => {
                successMessage.style.display = "none";
            }, 3000);
        });

        // Inicializar slideshow con datos de demo
        (async () => {
            const CLASS_NAME = 'slideshow';
            const CLASS_NAME_TARGET = "demo-slideshow";
            const TARGET = document.querySelector(`.${CLASS_NAME_TARGET}`);
            
            // Datos simulados de PPTX procesado
            const seminarData = {
                data: [{
                    id: 'demo-pptx-001',
                    title: 'Presentación Demo con Videos',
                    slideShow: JSON.stringify([
                        {
                            type: 'image',
                            imgUrl: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
                            note: '<h3>Slide 1: Introducción</h3><p>Esta es una imagen tradicional de Google Drive.</p><ul><li>Punto importante 1</li><li>Punto importante 2</li></ul>',
                            hasVideo: false
                        },
                        {
                            type: 'video',
                            videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
                            thumbnailUrl: 'https://sample-videos.com/zip/10/jpg/SampleJPGImage_1280x720_1mb.jpg',
                            note: '<h3>Slide 2: Video Explicativo</h3><p>Este slide contiene un video embebido extraído del PPTX.</p><ul><li>Video de demostración</li><li>Controles sincronizados</li></ul>',
                            hasVideo: true
                        },
                        {
                            type: 'image',
                            imgUrl: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
                            note: '<h3>Slide 3: Gráficos</h3><p>Slide con gráficos e información estadística.</p><ul><li>Datos importantes</li><li>Análisis detallado</li></ul>',
                            hasVideo: false
                        },
                        {
                            type: 'video',
                            videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                            thumbnailUrl: 'https://storage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg',
                            note: '<h3>Slide 4: Segundo Video</h3><p>Otro ejemplo de video integrado en la presentación.</p><ul><li>Video de alta calidad</li><li>Reproducción fluida</li></ul>',
                            hasVideo: true
                        },
                        {
                            type: 'mixed',
                            imgUrl: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
                            videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_640x360_1mb.mp4',
                            note: '<h3>Slide 5: Contenido Mixto</h3><p>Este slide combina imagen de fondo con video superpuesto.</p><ul><li>Imagen + Video</li><li>Experiencia rica</li></ul>',
                            hasVideo: true,
                            isMixed: true
                        }
                    ])
                }]
            };

            const { id = null, title = null, slideShow: _slideShow = [] } = seminarData.data[0];
            const STORAGE = `EVENT.PUB-${id}-${CLASS_NAME_TARGET}`;
            let tab = null;

            const translationText = {
                es: {
                    slideshow: ['Página', 'Pantalla para estudiantes', 'Abrir zoom', 'Ir a la libreria', 'Pantalla completa'],
                },
                en: {
                    slideshow: ['Page', 'Student screen', 'Open zoom', 'Go to library', 'Full screen'],
                }
            };

            const translation = translationText.es; // Usar español por defecto

            console.log('🎥 Demo iniciado con', JSON.parse(_slideShow).length, 'slides');
            console.log('📊 Tipos de contenido:', JSON.parse(_slideShow).map(s => s.type));

            // Inicializar el slideshow con videos
            const slideshow = new VideoSlideshow({
                target: CLASS_NAME_TARGET,
                slides: JSON.parse(_slideShow),
                id: id,
                title: title,
                translation: translation
            });

            console.log('✅ Slideshow con videos inicializado correctamente');
        })();
    </script>
</body>
</html>
