<?php
  $seminarname = $_GET['seminarname'];
  $class_name = strtolower(trim($seminarname));
  $class_name = preg_replace('/\s+/', '-', $class_name);
?>

<ul class="slideshow__tab">
	
	<li><a href="https://culturaviva.org/seminar?seminarname=<?php echo $seminarname;?>&lang=_es">Español</a></li>
  <li><a href="https://culturaviva.org/seminar?seminarname=<?php echo $seminarname;?>">Ingles</a></li>
  
</ul>

<div
  class="<?php echo $class_name; ?>"
  style="height: 725px; display: flex; justify-content: center; align-items: center; margin: 100px 0;"
></div>

<!-- Copy Link Button -->
<div style="text-align: center; margin-top: 15px;">
  <button id="copyLinkBtn" style="padding: 10px 20px; background-color: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">
    Copiar Enlace de Encuesta 
  </button>
  <span id="copySuccess" style="color: green; margin-left: 10px; display: none;">Link copied!</span>
</div>
<script>
	 document.getElementById('copyLinkBtn').addEventListener('click', function() {
    // Link to be copied
    const surveyLink = "https://culturaviva.org/student-survey/";

    // Create a temporary input element
    const tempInput = document.createElement("input");
    tempInput.value = surveyLink;
    document.body.appendChild(tempInput);
    
    // Select and copy the link
    tempInput.select();
    document.execCommand("copy");
    
    // Remove the temporary element
    document.body.removeChild(tempInput);
    
    // Show success message
    const successMessage = document.getElementById("copySuccess");
    successMessage.style.display = "inline";
    
    // Hide success message after 2 seconds
    setTimeout(() => {
      successMessage.style.display = "none";
    }, 2000);
  });

  (async () => {
	const CLASS_NAME = 'slideshow';
    const CLASS_NAME_TARGET = "<?php echo $class_name; ?>";
    const TARGET = document.querySelector(`.${CLASS_NAME_TARGET}`);
	const { getParams, send } = window.tool;
    // MODO DEMO LOCAL - Datos simulados de PPTX procesado
    const seminarData = {
      data: [{
        id: 'demo-pptx-001',
        title: 'Presentación Demo con Videos',
        slideShow: JSON.stringify([
          {
            type: 'image',
            imgUrl: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms', // Ejemplo de Google Sheets
            note: '<h3>Slide 1: Introducción</h3><p>Esta es una imagen tradicional de Google Drive.</p><ul><li>Punto importante 1</li><li>Punto importante 2</li></ul>',
            hasVideo: false
          },
          {
            type: 'video',
            videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
            thumbnailUrl: 'https://sample-videos.com/zip/10/jpg/SampleJPGImage_1280x720_1mb.jpg',
            note: '<h3>Slide 2: Video Explicativo</h3><p>Este slide contiene un video embebido extraído del PPTX.</p><ul><li>Video de demostración</li><li>Controles sincronizados</li></ul>',
            hasVideo: true
          },
          {
            type: 'image',
            imgUrl: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
            note: '<h3>Slide 3: Gráficos</h3><p>Slide con gráficos e información estadística.</p><ul><li>Datos importantes</li><li>Análisis detallado</li></ul>',
            hasVideo: false
          },
          {
            type: 'video',
            videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
            thumbnailUrl: 'https://storage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg',
            note: '<h3>Slide 4: Segundo Video</h3><p>Otro ejemplo de video integrado en la presentación.</p><ul><li>Video de alta calidad</li><li>Reproducción fluida</li></ul>',
            hasVideo: true
          },
          {
            type: 'mixed',
            imgUrl: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
            videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_640x360_1mb.mp4',
            note: '<h3>Slide 5: Contenido Mixto</h3><p>Este slide combina imagen de fondo con video superpuesto.</p><ul><li>Imagen + Video</li><li>Experiencia rica</li></ul>',
            hasVideo: true,
            isMixed: true
          }
        ])
      }]
    };

    // Comentar la llamada real para modo demo
    /*
    const seminarData = await send({
  	  endpoint: `https://abilityseminarsgroup.com/wp-json/api/v1/seminar-slideshow?seminarname=<?php echo urlencode($seminarname); ?>${getParams('lang') || ''}`,
  	  loading: { color: '#091B41', target: TARGET }
	}).get();
    */

    const { id = null, title = null, slideShow: _slideShow = [] } = seminarData.data[0];
    const STORAGE = `EVENT.PUB-${id}-${CLASS_NAME_TARGET}`;
    let tab = null;
	const translationText = {
	  es: {
	    slideshow: ['Página', 'Pantalla para estudiantes', 'Abrir zoom', 'Ir a la libreria', 'Pantalla completa'],
	  },
	  en: {
	    slideshow: ['Page', 'Student screen', 'Open zoom', 'Go to library', 'Full screen'],
	  }
	};
	

const translation = window.location.href.includes('lang=_es') 
    ? translationText.es 
    : (window.location.href.includes('/en/') || !window.location.href.includes('lang=_es')) 
        ? translationText.en 
        : translationText.es;


    /**
     * Opens a new browser tab with specified properties.
     * @param {object} tabProperties - An object containing properties for the new tab.
     * @param {string} tabProperties.id - The ID of the new tab.
     * @param {number} tabProperties.index - The index of the new tab.
     * @param {string[]} tabProperties.srcs - An array of source URLs to be loaded in the new tab.
     * @param {string} tabProperties.title - The title of the new tab.
     */
    const openNewTab = ({ id, index, srcs, title }) => {
      if (tab === null) {
        const script = document.createElement('script');
        const CLASS_NAME = 'tab';

		const getScreen = (hw) =>
		  window.screen[hw] * 0.60;

		 tab = window.open(
      'about:blank',
      '_blank',
      'height=' + getScreen('height') + ', width=' + getScreen('width')
    );

    const template =
      '<!DOCTYPE html>' +
      '<html lang="en">' +
      '<head>' +
      '<meta charset="UTF-8"/>' +
      '<meta name="viewport" content="width=device-width, initial-scale=1.0"/>' +
      '<title>Student window</title>' +
      '<style>' +
      'body { padding: 0; margin: 0; background: #000; }' +
      '.' + CLASS_NAME + '__container { align-items: center; display: flex; height: 100vh; justify-content: center; width: 100%; position: relative; }' +
      '.' + CLASS_NAME + '__img-current { height: 100%; width: 100%; object-fit: contain; display: none; }' +
      '.' + CLASS_NAME + '__video-current { height: 100%; width: 100%; object-fit: contain; display: none; }' +
      '.' + CLASS_NAME + '__mixed-current { height: 100%; width: 100%; position: relative; display: none; }' +
      '.' + CLASS_NAME + '__mixed-current img { width: 100%; height: 100%; object-fit: contain; }' +
      '.' + CLASS_NAME + '__mixed-current video { position: absolute; bottom: 20px; right: 20px; width: 30%; height: 30%; border: 3px solid white; border-radius: 8px; }' +
      '.show { display: block !important; }' +
      '</style>' +
      '</head>' +
      '<body>' +
      '<main>' +
      '<div class="' + CLASS_NAME + '__container">' +
      '<img class="' + CLASS_NAME + '__img-current" />' +
      '<video class="' + CLASS_NAME + '__video-current" controls preload="metadata" />' +
      '<div class="' + CLASS_NAME + '__mixed-current">' +
      '<img />' +
      '<video controls preload="metadata" />' +
      '</div>' +
      '</div>' +
      '</main>' +
      '</body>' +
      '</html>';

    script.innerHTML =
      "let data = {}; " +
      "const onStorage = ({ newValue, key }) => { " +
      "  const request = JSON.parse(newValue); " +
      "  if (request && key === '" + STORAGE + "') { update(request.data); } " +
      "}; " +
      "const update = async ({ index, title, srcs }) => { " +
      "  const imgCurrent = document.querySelector('." + CLASS_NAME + "__img-current'); " +
      "  const videoCurrent = document.querySelector('." + CLASS_NAME + "__video-current'); " +
      "  const mixedCurrent = document.querySelector('." + CLASS_NAME + "__mixed-current'); " +
      "  const item = srcs[index]; " +
      "  " +
      "  // Ocultar todos los elementos " +
      "  imgCurrent.classList.remove('show'); " +
      "  videoCurrent.classList.remove('show'); " +
      "  mixedCurrent.classList.remove('show'); " +
      "  " +
      "  // Pausar videos activos " +
      "  videoCurrent.pause(); " +
      "  const mixedVideo = mixedCurrent.querySelector('video'); " +
      "  if (mixedVideo) mixedVideo.pause(); " +
      "  " +
      "  if (item) { " +
      "    if (item.type === 'video') { " +
      "      videoCurrent.src = item.videoUrl; " +
      "      videoCurrent.classList.add('show'); " +
      "      videoCurrent.muted = true; " +
      "      setTimeout(() => videoCurrent.play().catch(e => console.log('Autoplay prevented')), 100); " +
      "    } else if (item.type === 'mixed') { " +
      "      const mixedImg = mixedCurrent.querySelector('img'); " +
      "      const mixedVid = mixedCurrent.querySelector('video'); " +
      "      mixedImg.src = 'https://drive.google.com/thumbnail?id=' + item.imgUrl + '&sz=w10000'; " +
      "      mixedVid.src = item.videoUrl; " +
      "      mixedCurrent.classList.add('show'); " +
      "      mixedVid.muted = true; " +
      "      setTimeout(() => mixedVid.play().catch(e => console.log('Autoplay prevented')), 100); " +
      "    } else { " +
      "      imgCurrent.src = 'https://drive.google.com/thumbnail?id=' + item.imgUrl + '&sz=w10000'; " +
      "      imgCurrent.classList.add('show'); " +
      "    } " +
      "  } " +
      "}; " +
      "window.addEventListener('storage', onStorage); ";

        const onClose = () => {
          tab.removeEventListener('beforeunload', onClose);
          tab = null;
          cleanStorage();
        };

        tab.document.open();
        tab.document.write(template);
        tab.document.body.appendChild(script);
        tab.addEventListener('beforeunload', onClose);
        tab.document.close();

        sendDataToTab({ id, index, srcs, title });
      } else {
        alert('You already have a new presentation opened.');
      }
    };
    /**
     * Sends data to the current tab by storing it in the local storage.
     * @param {any} data - The data to be sent to the tab.
     */
    const sendDataToTab = (data) => {
      cleanStorage();
      localStorage.setItem(STORAGE, JSON.stringify({ data }));
    };
    /**
     * Cleans up the storage by removing the stored data.
     */
    const cleanStorage = () => {
      localStorage.removeItem(STORAGE);
    };  
	/**
     * Retrieves DOM element(s) with the specified class name.
     * @param {string} className - The class name of the element(s) to retrieve.
     * @param {boolean} [isAll=false] - Specifies whether to retrieve all matching elements (default: false).
     * @returns {(Element|NodeList)} Returns the single matching element or a list of matching elements.
     */
	const $ = (className, isAll=false) => {
      const TARGET = `.${CLASS_NAME_TARGET} .${CLASS_NAME}__${className}`;

	   return isAll ? document.querySelectorAll(TARGET) : document.querySelector(TARGET);
	}
    /**
     * Generate a Google Drive thumbnail image URL with the provided ID or return video URL.
     * @param {object|string} item - The item object or ID of the image in Google Drive.
     * @returns {string} The URL of the thumbnail image or video.
     */
    const getSrc = (item) => {
      if (typeof item === 'string') {
        return 'https://drive.google.com/thumbnail?id='+item+'&sz=w10000';
      }

      if (item.type === 'video') {
        return item.videoUrl;
      } else if (item.type === 'mixed') {
        return 'https://drive.google.com/thumbnail?id='+item.imgUrl+'&sz=w10000';
      }

      return 'https://drive.google.com/thumbnail?id='+item.imgUrl+'&sz=w10000';
    };

    /**
     * Get thumbnail for panel display
     * @param {object} item - The slide item
     * @returns {string} Thumbnail URL
     */
    const getThumbnail = (item) => {
      if (item.type === 'video' && item.thumbnailUrl) {
        return item.thumbnailUrl;
      } else if (item.type === 'video') {
        return item.videoUrl; // Fallback to video URL
      }
      return 'https://drive.google.com/thumbnail?id='+item.imgUrl+'&sz=w10000';
    };
    /**
     * Creates a slideshow with specified properties.
     * @param {object} slideshowProperties - An object containing properties for the slideshow.
     * @param {string} slideshowProperties.id - The ID of the slideshow.
     * @param {string} slideshowProperties.title - The title of the slideshow.
     * @param {string[]} [slideshowProperties.slideShow=[]] - An array of source URLs for the slideshow slides.
     */
    const slideShow = ({ id, title, slideShow: srcs = [] }) => {
      if (srcs.length === 0) {
        alert('You don\'t have any presentations to show.');
        return;
      }

      let index = 0;
      const SIZE = srcs.length - 1;
      const CLASS_NAME_FULL_SCREEN = `${CLASS_NAME}__img--full-screen`;

      requestAnimationFrame(() => {
        const imgContainer = $('container-img');
        const container = $('container');
        const OPACITY = 0;
        /**
         * Checks if it is possible to navigate to the next slide.
         * @returns {boolean} Returns true if it is possible to navigate to the next slide, otherwise false.
         */
        const canNext = () => index < SIZE;
        /**
         * Checks if it is possible to navigate to the previous slide.
         * @returns {boolean} Returns true if it is possible to navigate to the previous slide, otherwise false.
         */
        const canPrev = () => index > 0;
        /**
         * Sets the opacity of the previous and next buttons based on the ability to navigate to previous and next slides.
         * @return {void}
         */
        const setOPacityArrow = () => {
          $('button--prev').style.opacity = canPrev() ? 1 : OPACITY;
          $('button--next').style.opacity = canNext() ? 1 : OPACITY;
        }
        /**
         * Updates the slideshow to display the slide corresponding to the specified index.
         * @param {number} index - The index of the slide to display.
         * @return {void}
         */
        const update = (index) => {
          const pagination = $('pagination-value');
          const currentContent = document.querySelector(`.${CLASS_NAME_TARGET} .${CLASS_NAME}__img.show, .${CLASS_NAME_TARGET} .${CLASS_NAME}__img video.show`);
          const contentSelected = document.querySelector(`.${CLASS_NAME_TARGET} .${CLASS_NAME}__container-img > *:nth-child(${index + 1})`);
          const imagePanel = $('panel-image--selected');
          const imagePanelSelected = $('panel-image', true)[index];
          const note = $('content-container-note');
		  const imagePanelContainer = $('content-container');
          const CLASS_NAME_IMAGE_PANEL_SELECTED = `${CLASS_NAME}__panel-image--selected`;

          // Pausar videos actuales antes de cambiar
          const currentVideos = document.querySelectorAll(`.${CLASS_NAME_TARGET} .${CLASS_NAME}__img video, .${CLASS_NAME_TARGET} .${CLASS_NAME}__img.show video`);
          currentVideos.forEach(video => {
            video.pause();
            video.currentTime = 0;
          });

          // Remover clases show de todos los elementos
          const allContent = document.querySelectorAll(`.${CLASS_NAME_TARGET} .${CLASS_NAME}__img`);
          allContent.forEach(el => el.classList.remove('show'));

          // Agregar clase show al elemento seleccionado
          if (contentSelected) {
            contentSelected.classList.add('show');

            // Si es un video, configurar autoplay
            const video = contentSelected.tagName === 'VIDEO' ? contentSelected : contentSelected.querySelector('video');
            if (video) {
              video.muted = true; // Necesario para autoplay
              setTimeout(() => {
                video.play().catch(e => console.log('Autoplay prevented:', e));
              }, 100);
            }
          }

          // Actualizar panel lateral
          if (imagePanelSelected) {
            imagePanelSelected.classList.add(CLASS_NAME_IMAGE_PANEL_SELECTED);
          }
          if (imagePanel) {
            imagePanel.classList.remove(CLASS_NAME_IMAGE_PANEL_SELECTED);
          }

          scrollToPosition({
            x: imagePanelSelected?.offsetLeft || 0,
            y: imagePanelSelected?.offsetTop || 0,
            el: imagePanelContainer,
          });

          pagination.innerText = `${translation.slideshow[0]}: ${index + 1} / ${SIZE + 1}`;
          note.innerHTML = getNote();

          sendDataToTab({ id, index, title, srcs });
          setOPacityArrow();
        };

        document.addEventListener('fullscreenchange', (event) => {
          !document.fullscreenElement && imgContainer.classList.remove(CLASS_NAME_FULL_SCREEN);
        });

        window.addEventListener('storage', (event) => {
          const request = event.newValue;

          if (request && event.key === STORAGE && tab) {
            const newData = JSON.parse(request);

            index = Number(newData.data.index);
            update(index);
          }
        });

        window.addEventListener('keydown', ({ key }) => {
          if (key === 'ArrowLeft' && canPrev()) {
            update(--index);
          } else if (key === 'ArrowRight' && canNext()) {
            update(++index);
          }
        });

        window.addEventListener('click', ({ target }) => {
          // Helper function to check if the event target has a specific class suffix
          const hasClass = (suffix) => window.tool.hasClassName(event, `${CLASS_NAME}__${suffix}`);

          // If clicking on a panel image, update index based on the image's dataset index
          if (hasClass('panel-image')) {
            index = Number(target.dataset.index);
            update(index);
          }

		  if (hasClass('button--zoom')) {
			const url = 'zoommtg://zoom.us';

			window.open(url, '_blank', 'width=800,height=600,scrollbars=yes');
		  }

          // Toggle fullscreen mode on clicking the fullscreen button
          if (hasClass('button--fullscreen')) {
            const slideshow = $('content');
            const contentContainers = $('content-container');
            const containerNote = $('content-container-note');

            // Check if currently in fullscreen mode, then exit or request fullscreen accordingly
            const isFullScreen = !!document.fullscreenElement;
            isFullScreen ? document.exitFullscreen() : container.requestFullscreen();

            // Toggle the fullscreen class and adjust width based on fullscreen state
            imgContainer.classList.toggle(CLASS_NAME_FULL_SCREEN, !isFullScreen);
            slideshow.style.width = isFullScreen ? '800px' : '72%';
          }

          // Go to the next image if clicking on the next button or container image and if next is available
          if (hasClass('img') || hasClass('button--next')) canNext() && update(++index);

          // Go to the previous image if clicking the previous button and if previous is available
          if (hasClass('button--prev')) canPrev() && update(--index);

          // Open a new tab with the image note if clicking the note button
          if (hasClass('button--note')) {
            tab && tab.close();  // Close any existing tab if open
            openNewTab({ id, index, title, srcs }); // Open the new tab with provided info
          }

          if (hasClass('button--dashboard')) {
            window.location.href = 'https://culturaviva.org/coach-library';
          }
        });
      });
      /**
       * Scrolls to a specific position within a specified element.
       *
       * @param {Object} options - The options for scrolling.
       * @param {string} options.el - The identifier for the target element (used in the class name).
       * @param {number} options.x - The horizontal scroll position (in pixels).
       * @param {number} options.y - The vertical scroll position (in pixels).
       * @param {string} [options.behavior='smooth'] - The scrolling behavior ('smooth' or 'auto').
       */
      const scrollToPosition = ({ el, x, y, behavior = 'smooth' }) => {
        el.scrollTo({ top: y, left: x });
      }
      /**
       * Generates HTML string for content elements (images/videos) based on provided class names and sources.
       *
       * @param {string} className - The base class name for the elements.
       * @param {boolean} isSelected - Whether to add selected class to first element.
       * @param {boolean} isShow - Whether to add show class to first element.
       * @returns {string} - The generated HTML string for the content.
       */
      const getImages = (className, isSelected, isShow) =>
        window.tool.template(srcs, (item, index) => {
          const baseClasses = `${CLASS_NAME}__${className}${index === 0 ? `${isSelected ? ` ${CLASS_NAME}__panel-image--selected` : '' }${isShow ? ' show' : ''}` : ''}`;

          // Para el panel lateral (miniaturas)
          if (className === 'panel-image') {
            return `<div class="${baseClasses}" data-index="${index}" data-type="${item.type}">
                      <img src="${getThumbnail(item)}" style="width: 100%; height: 100%; object-fit: cover;" />
                      ${item.hasVideo ? '<div class="video-indicator">▶</div>' : ''}
                    </div>`;
          }

          // Para el contenido principal
          if (item.type === 'video') {
            return `<video
                      class="${baseClasses}"
                      src="${item.videoUrl}"
                      data-index="${index}"
                      data-type="video"
                      controls
                      preload="metadata"
                      ${index === 0 && isShow ? 'autoplay muted' : ''}
                      style="width: 100%; height: 100%; object-fit: contain;"
                    />`;
          } else if (item.type === 'mixed') {
            return `<div class="${baseClasses}" data-index="${index}" data-type="mixed" style="position: relative; width: 100%; height: 100%;">
                      <img src="${getSrc(item)}" style="width: 100%; height: 100%; object-fit: contain;" />
                      <video
                        src="${item.videoUrl}"
                        controls
                        preload="metadata"
                        style="position: absolute; bottom: 10px; right: 10px; width: 30%; height: 30%; border: 2px solid white;"
                        ${index === 0 && isShow ? 'autoplay muted' : ''}
                      />
                    </div>`;
          } else {
            return `<img
                      class="${baseClasses}"
                      src="${getSrc(item)}"
                      data-index="${index}"
                      data-type="image"
                    />`;
          }
        });
      /**
       * Gets the note associated with the currently selected image.
       *
       * @returns {string} - The note for the image at the current index.
       */
      const getNote = () => srcs[index].note;

      return `
      <div class="${CLASS_NAME} focus">
        <div class="${CLASS_NAME}__container">
          <div class="${CLASS_NAME}__content-container">
  			<div class="${CLASS_NAME}__container-imgs">
              ${getImages('panel-image', true)}
			</div>
          </div>
          <div class="${CLASS_NAME}__prev">
            <span class="${CLASS_NAME}__button--prev">
              &#10094;
            </span>
          </div>
          <div class="${CLASS_NAME}__content">
            <div class="${CLASS_NAME}__container-img">
              ${getImages('img', false, true)}
            </div>
            <div class="${CLASS_NAME}__control">
              <div class="${CLASS_NAME}__pagination">
                <span class="${CLASS_NAME}__pagination-value">
                  ${translation.slideshow[0]}: 1 / ${SIZE + 1}
                </span>
              </div>
              <div class="${CLASS_NAME}__note">
                <span class="${CLASS_NAME}__button--note">
                  ${translation.slideshow[1]}
                </span>
              </div>

			<div class="${CLASS_NAME}__note">
                <span class="${CLASS_NAME}__button--zoom">
                   ${translation.slideshow[2]}
                </span>
              </div>
            
              <div class="${CLASS_NAME}__note">
                <span class="${CLASS_NAME}__button--dashboard">
     			  ${translation.slideshow[3]}
                </span>
              </div>
              <div class="${CLASS_NAME}__fullscreen">
                <span class="${CLASS_NAME}__button--fullscreen">
                  ${translation.slideshow[4]}
                </span>
              </div>
            </div>
          </div>
          <div class="${CLASS_NAME}__content-container">
            <div class="${CLASS_NAME}__content-container-note">${getNote()}</div>
          </div>
          <div class="${CLASS_NAME}__next">
            <span class="${CLASS_NAME}__button--next"> 
              &#10095;
            </span>
          </div>
        </div>
      </div>
    `;
    };
    // Append the HTML content generated by the slideShow function to the innerHTML of the TARGET element
    TARGET.innerHTML += slideShow({ id, title, slideShow: JSON.parse(_slideShow) });
  })();
</script>
<style>
/* Estilos originales para desktop */
.slideshow__tab {
  margin-top: 30px; 
  display: flex;
  justify-content: center;
}

.slideshow__tab li {
  list-style: none;	 
}

.slideshow__tab a {
  padding: 10px 20px;
  margin-left: 10px;
  background-color: #4672c4;
  color: white;
  font-weight: bold;
  text-decoration: none;
  border-radius: 5px;
}

.slideshow__container-img {
  height: 722px;
}

.slideshow__img--full-screen {
  height: calc(100vh - 30px);
}

.slideshow__img {
  display: none;
}

.slideshow__img.show {
  display: block;
  height: 100%;
  width: 100%;
  object-fit: contain;
}

/* Estilos para videos */
.slideshow__img video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Estilos para contenido mixto */
.slideshow__img[data-type="mixed"] {
  position: relative;
}

.slideshow__img[data-type="mixed"] video {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 30%;
  height: 30%;
  border: 2px solid white;
  border-radius: 8px;
}

/* Indicador de video en panel */
.video-indicator {
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  pointer-events: none;
}

.slideshow__panel-image {
  position: relative;
}

.slideshow {
  display: flex;
  font-size: 20px;
  height: 100%;
  justify-content: center;
}

.slideshow__container {
  align-items: center;
  display: flex;
  font-family: Arial, Helvetica, sans-serif;
  justify-content: center;
  position: relative;
}

.slideshow__content,
.slideshow__container,
.slideshow__img {
  width: 100%;
}

.slideshow__img {
  height: 100%;
}

.slideshow__content {
  width: 1000px;
}

.slideshow__button--prev {
  opacity: 0;
}

.slideshow__button--prev,
.slideshow__button--next,
.slideshow__button--zoom {
  cursor: pointer;
}

.slideshow__button--prev,
.slideshow__button--next {
  border: 1px solid;
  border-radius: 5px;
  font-size: 40px;
  padding: 20px;
}

.slideshow__next,
.slideshow__prev {
  color: #4572c4;
  position: absolute;
}

.slideshow__next {
  right: -80px;
}

.slideshow__prev {
  left: -80px;
}

.slideshow__control {
  border-top: 1px solid;
  background: white;
  display: flex;
  justify-content: space-around;
  padding: 10px;
  font-size: 18px;
}

.slideshow__button--fullscreen,
.slideshow__button--note,
.slideshow__button--dashboard {
  cursor: pointer;
}

.slideshow__content-container {
  border: 1px solid;
  background-color: white;
  color: black;
  font-size: 20px;
  height: calc(50px + 100%);
  overflow: auto;
  padding-top: 25px;
  padding: 0 20px;
  width: 300px;
  scrollbar-width: thin;
  scrollbar-color: #4572c4 #ffffff;
}

.slideshow__content-container:nth-child(1) {
  width: 261px;
}

.slideshow__panel-image {
  border: solid 2px #d7d7d7 !important;
  cursor: pointer;
  margin-top: 10px;
  padding: 10px;
  height: 122.84px !important;
  width: 200px;
}

.slideshow__panel-image--selected {
  border: solid 2px #3f51b5 !important;
}

.slideshow__content-container-note,
.slideshow__container-imgs {
  margin-top: 50px;
}

.slideshow_content-container-note li {
  margin-bottom: 20px;
}

.slideshow__content-container-note ul {
  padding: 10px;
}

.slideshow__content {
  border: 1px solid;
  border-left: none;
  border-right: none;
}

/* Media queries para dispositivos móviles y tablets */
@media (max-width: 1023px) {
  .slideshow__container {
    flex-direction: column;
    padding: 10px;
  }

  .slideshow__content {
    width: 100%;
    border: 1px solid;
  }

  .slideshow__container-img {
    height: auto;
    max-height: 50vh;
  }

  .slideshow__img.show {
    width: 100%;
    height: auto;
    max-height: 50vh;
    object-fit: contain;
  }

  /* Estilos responsive para videos */
  .slideshow__img video {
    width: 100%;
    height: auto;
    max-height: 50vh;
    object-fit: contain;
  }

  .slideshow__img[data-type="mixed"] video {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 40%;
    height: 40%;
    border: 1px solid white;
  }

  .slideshow__content-container {
    width: 100% !important;
    height: auto;
    max-height: 200px;
    border: none;
    border-top: 1px solid;
    margin-top: 10px;
  }

  .slideshow__content-container:nth-child(1) {
    width: 100% !important;
    order: 2;
  }

  .slideshow__container-imgs {
    display: flex;
    overflow-x: auto;
    margin-top: 10px;
    padding-bottom: 10px;
  }

  .slideshow__panel-image {
    flex: 0 0 auto;
    width: 150px !important;
    height: 100px !important;
    margin-right: 10px;
  }

  .slideshow__prev,
  .slideshow__next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }

  .slideshow__prev {
    left: 10px;
  }

  .slideshow__next {
    right: 10px;
  }

  .slideshow__button--prev,
  .slideshow__button--next {
    font-size: 24px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.8);
  }

  .slideshow__control {
    flex-wrap: wrap;
    gap: 10px;
  }

  .slideshow__control > div {
    flex: 1 1 auto;
    min-width: 45%;
    text-align: center;
  }

  .slideshow__tab {
    flex-wrap: wrap;
    gap: 10px;
  }

  .slideshow__tab a {
    margin-left: 0;
  }
}

/* Ajustes adicionales para tablets */
@media (min-width: 768px) and (max-width: 1023px) {
  .<?php echo $class_name; ?> {
    height: auto !important;
    padding: 20px 0;
  }

  /* Ajustamos el contenedor de panel de imágenes */
  .slideshow__container-imgs {
    display: flex;
    overflow-x: auto;
    justify-content: center;
    padding-bottom: 30px; /* Aumenta el espacio inferior para que no se corte */
    max-width: 100%;
  }

  /* Reducimos ligeramente la altura de cada imagen del panel */
  .slideshow__panel-image {
    width: 160px !important;
    height: 90px !important; /* Disminuye la altura para que se ajuste mejor */
    margin-right: 10px;
    flex-shrink: 0;
  }
	
	.slideshow__container{
		margin-top: -85px;
	}
	
}
</style>